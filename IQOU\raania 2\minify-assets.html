<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS and JS Minifier</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clean-css/4.2.3/clean-css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/terser/5.7.0/bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .file-section {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        textarea {
            width: 100%;
            min-height: 200px;
            font-family: monospace;
            padding: 10px;
            margin: 10px 0;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .stats {
            margin-top: 10px;
            font-size: 14px;
        }
        .tabs {
            display: flex;
            margin-bottom: 10px;
        }
        .tab {
            padding: 8px 16px;
            cursor: pointer;
            border: 1px solid #ddd;
            background-color: #f1f1f1;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        #cssSection, #jsSection {
            display: none;
        }
        #cssSection.active, #jsSection.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>CSS and JavaScript Minifier</h1>
    <p>This tool helps minify CSS and JavaScript files to improve website performance.</p>
    
    <div class="tabs">
        <div class="tab active" id="cssTab">CSS Minifier</div>
        <div class="tab" id="jsTab">JavaScript Minifier</div>
    </div>
    
    <div class="container">
        <div class="file-section active" id="cssSection">
            <h2>CSS Minifier</h2>
            <div>
                <h3>Input CSS</h3>
                <input type="file" id="cssFileInput" accept=".css">
                <p>Or paste your CSS code below:</p>
                <textarea id="cssInput" placeholder="Paste your CSS code here..."></textarea>
            </div>
            
            <button id="minifyCss">Minify CSS</button>
            
            <div>
                <h3>Output (Minified CSS)</h3>
                <div class="stats" id="cssStats"></div>
                <textarea id="cssOutput" readonly placeholder="Minified CSS will appear here..."></textarea>
                <button id="downloadCss">Download Minified CSS</button>
                <button id="copyCss">Copy to Clipboard</button>
            </div>
        </div>
        
        <div class="file-section" id="jsSection">
            <h2>JavaScript Minifier</h2>
            <div>
                <h3>Input JavaScript</h3>
                <input type="file" id="jsFileInput" accept=".js">
                <p>Or paste your JavaScript code below:</p>
                <textarea id="jsInput" placeholder="Paste your JavaScript code here..."></textarea>
            </div>
            
            <button id="minifyJs">Minify JavaScript</button>
            
            <div>
                <h3>Output (Minified JavaScript)</h3>
                <div class="stats" id="jsStats"></div>
                <textarea id="jsOutput" readonly placeholder="Minified JavaScript will appear here..."></textarea>
                <button id="downloadJs">Download Minified JavaScript</button>
                <button id="copyJs">Copy to Clipboard</button>
            </div>
        </div>
    </div>

    <script>
        // Tab switching
        const cssTab = document.getElementById('cssTab');
        const jsTab = document.getElementById('jsTab');
        const cssSection = document.getElementById('cssSection');
        const jsSection = document.getElementById('jsSection');
        
        cssTab.addEventListener('click', () => {
            cssTab.classList.add('active');
            jsTab.classList.remove('active');
            cssSection.style.display = 'block';
            jsSection.style.display = 'none';
        });
        
        jsTab.addEventListener('click', () => {
            jsTab.classList.add('active');
            cssTab.classList.remove('active');
            jsSection.style.display = 'block';
            cssSection.style.display = 'none';
        });
        
        // CSS Minification
        const cssFileInput = document.getElementById('cssFileInput');
        const cssInput = document.getElementById('cssInput');
        const cssOutput = document.getElementById('cssOutput');
        const cssStats = document.getElementById('cssStats');
        const minifyCssBtn = document.getElementById('minifyCss');
        const downloadCssBtn = document.getElementById('downloadCss');
        const copyCssBtn = document.getElementById('copyCss');
        
        cssFileInput.addEventListener('change', handleCssFileUpload);
        minifyCssBtn.addEventListener('click', minifyCss);
        downloadCssBtn.addEventListener('click', downloadCssFile);
        copyCssBtn.addEventListener('click', () => copyToClipboard(cssOutput));
        
        function handleCssFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                cssInput.value = e.target.result;
            };
            reader.readAsText(file);
        }
        
        function minifyCss() {
            const css = cssInput.value;
            if (!css) {
                alert('Please enter or upload CSS code');
                return;
            }
            
            try {
                const cleanCSS = new CleanCSS({
                    level: 2,
                    format: 'keep-breaks'
                });
                const minified = cleanCSS.minify(css);
                
                cssOutput.value = minified.styles;
                
                // Calculate stats
                const originalSize = new Blob([css]).size;
                const minifiedSize = new Blob([minified.styles]).size;
                const savedBytes = originalSize - minifiedSize;
                const savingPercentage = Math.round((savedBytes / originalSize) * 100);
                
                cssStats.innerHTML = `
                    <p>Original size: ${formatBytes(originalSize)}</p>
                    <p>Minified size: ${formatBytes(minifiedSize)}</p>
                    <p>Saved: ${formatBytes(savedBytes)} (${savingPercentage}%)</p>
                `;
            } catch (error) {
                console.error('Error minifying CSS:', error);
                alert(`Error minifying CSS: ${error.message}`);
            }
        }
        
        function downloadCssFile() {
            if (!cssOutput.value) {
                alert('Please minify CSS first');
                return;
            }
            
            downloadFile(cssOutput.value, 'styles.min.css', 'text/css');
        }
        
        // JavaScript Minification
        const jsFileInput = document.getElementById('jsFileInput');
        const jsInput = document.getElementById('jsInput');
        const jsOutput = document.getElementById('jsOutput');
        const jsStats = document.getElementById('jsStats');
        const minifyJsBtn = document.getElementById('minifyJs');
        const downloadJsBtn = document.getElementById('downloadJs');
        const copyJsBtn = document.getElementById('copyJs');
        
        jsFileInput.addEventListener('change', handleJsFileUpload);
        minifyJsBtn.addEventListener('click', minifyJs);
        downloadJsBtn.addEventListener('click', downloadJsFile);
        copyJsBtn.addEventListener('click', () => copyToClipboard(jsOutput));
        
        function handleJsFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                jsInput.value = e.target.result;
            };
            reader.readAsText(file);
        }
        
        async function minifyJs() {
            const js = jsInput.value;
            if (!js) {
                alert('Please enter or upload JavaScript code');
                return;
            }
            
            try {
                const minified = await Terser.minify(js, {
                    compress: {
                        drop_console: false,
                        drop_debugger: true
                    },
                    mangle: true
                });
                
                jsOutput.value = minified.code;
                
                // Calculate stats
                const originalSize = new Blob([js]).size;
                const minifiedSize = new Blob([minified.code]).size;
                const savedBytes = originalSize - minifiedSize;
                const savingPercentage = Math.round((savedBytes / originalSize) * 100);
                
                jsStats.innerHTML = `
                    <p>Original size: ${formatBytes(originalSize)}</p>
                    <p>Minified size: ${formatBytes(minifiedSize)}</p>
                    <p>Saved: ${formatBytes(savedBytes)} (${savingPercentage}%)</p>
                `;
            } catch (error) {
                console.error('Error minifying JavaScript:', error);
                alert(`Error minifying JavaScript: ${error.message}`);
            }
        }
        
        function downloadJsFile() {
            if (!jsOutput.value) {
                alert('Please minify JavaScript first');
                return;
            }
            
            downloadFile(jsOutput.value, 'script.min.js', 'text/javascript');
        }
        
        // Utility functions
        function downloadFile(content, fileName, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            link.click();
            
            URL.revokeObjectURL(url);
        }
        
        function copyToClipboard(textArea) {
            if (!textArea.value) {
                alert('Nothing to copy');
                return;
            }
            
            textArea.select();
            document.execCommand('copy');
            alert('Copied to clipboard!');
        }
        
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
