document.addEventListener("DOMContentLoaded",function(){const e=document.querySelector(".mobile-menu-btn"),t=document.querySelector("nav");if(e&&(e.addEventListener("click",function(){t.classList.toggle("active")}),document.addEventListener("click",function(n){!t.contains(n.target)&&!e.contains(n.target)&&t.classList.contains("active")&&t.classList.remove("active")}),document.querySelectorAll("nav a").forEach(e=>{e.addEventListener("click",function(){t.classList.remove("active")})})),document.querySelectorAll(".faq-item").forEach(e=>{const t=e.querySelector(".faq-question");t&&t.addEventListener("click",function(){document.querySelectorAll(".faq-item").forEach(t=>{t!==e&&t.classList.remove("active")}),e.classList.toggle("active")})}),document.getElementById("contact-form")){document.getElementById("contact-form").addEventListener("submit",function(e){e.preventDefault();const t=document.getElementById("name").value,n=document.getElementById("company").value,c=document.getElementById("email").value,o=document.getElementById("phone").value,a=document.getElementById("interest").value,i=document.getElementById("message").value;if(!t||!n||!c||!o||!a||!i)return void alert("Please fill in all required fields.");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c))return void alert("Please enter a valid email address.");alert("Thank you for your inquiry! We will contact you within 24 hours."),this.reset()})}document.querySelectorAll('a[href^="#"]').forEach(e=>{e.addEventListener("click",function(e){e.preventDefault();const t=this.getAttribute("href");if("#"===t)return;const n=document.querySelector(t);n&&window.scrollTo({top:n.offsetTop-100,behavior:"smooth"})})}),window.toggleFaq=function(e){const t=e.parentElement;document.querySelectorAll(".faq-item").forEach(e=>{e!==t&&e.classList.remove("active")}),t.classList.toggle("active")}});
