/* Base Styles */
:root {
    --primary-color: #00563B; /* Deeper green - representing Pakistani heritage */
    --secondary-color: #D4B254; /* Rich gold - representing premium quality */
    --accent-color: #8A6D3B; /* Warm accent for highlights */
    --dark-color: #222222; /* Darker for more contrast */
    --light-color: #f9f9f9; /* Slightly warmer light background */
    --text-color: #333333; /* Darker text for better readability */
    --white: #ffffff;
    --gray: #f6f6f6; /* Slightly warmer gray */
    --border-color: #e5e5e5; /* Slightly warmer border */
    --shadow: 0 5px 20px rgba(0, 0, 0, 0.08); /* More subtle, premium shadow */
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* Deeper shadow for cards */
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Raleway', sans-serif;
    color: var(--text-color);
    line-height: 1.7; /* Increased line height for better readability */
    background-color: var(--white);
    font-size: 16px; /* Base font size */
    font-weight: 400;
    letter-spacing: 0.01em; /* Slight letter spacing for elegance */
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    color: var(--dark-color);
    margin-bottom: 1.2rem;
    line-height: 1.3;
    font-weight: 700; /* Bolder headings */
    letter-spacing: -0.02em; /* Tighter letter spacing for headings */
}

h1 {
    font-size: 2.8rem; /* Larger heading */
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
    text-align: center;
}

h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
    border-radius: 2px;
}

h3 {
    font-size: 1.6rem;
    margin-bottom: 1.2rem;
}

p {
    margin-bottom: 1.2rem;
    font-size: 1.05rem; /* Slightly larger paragraph text */
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.section-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 2rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 14px 30px;
    border-radius: 3px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-size: 0.9rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.6s ease;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #004a32;
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--dark-color);
}

.btn-secondary:hover {
    background-color: #c19b2e;
    color: var(--dark-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* Header */
header {
    background-color: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 70px;
    object-fit: contain;
    border-radius: 4px;
    transition: var(--transition);
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 35px;
}

nav ul li a {
    color: var(--dark-color);
    font-weight: 600;
    padding: 10px 0;
    position: relative;
    font-size: 1.05rem;
    letter-spacing: 0.02em;
    transition: color 0.3s ease;
}

nav ul li a:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    bottom: 0;
    left: 0;
    transition: width 0.3s ease;
}

nav ul li a:hover {
    color: var(--primary-color);
}

nav ul li a:hover:after,
nav ul li a.active:after {
    width: 100%;
}

.mobile-menu-btn {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)), url('images/hero-rice-fields.jpg');
    background-size: cover;
    background-position: center;
    color: var(--white);
    padding: 140px 0;
    text-align: center;
    position: relative;
    will-change: transform; /* Optimize for GPU acceleration */
}

.hero:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    z-index: 1;
}

.hero-content {
    max-width: 850px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.2rem;
    color: var(--white);
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.hero .subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    font-weight: 400;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 25px;
}

/* Page Banner */
.page-banner {
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('images/page-banner.jpg');
    background-size: cover;
    background-position: center;
    color: var(--white);
    padding: 80px 0;
    text-align: center;
}

.page-banner h1 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.page-banner p {
    font-size: 1.2rem;
}

/* Value Proposition Section */
.value-proposition {
    padding: 80px 0;
    text-align: center;
}

.value-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.value-card {
    background-color: var(--white);
    padding: 40px 30px;
    border-radius: 6px;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    border-top: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.value-card:hover {
    transform: translateY(-10px);
}

.value-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 86, 59, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
    z-index: 0;
}

.value-card .icon {
    font-size: 2.8rem;
    color: var(--primary-color);
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

/* Featured Products Section */
.featured-products {
    padding: 90px 0;
    background-color: var(--light-color);
    text-align: center;
}

.featured-products .section-intro {
    max-width: 800px;
    margin: 0 auto 40px;
    font-size: 1.1rem;
}

.product-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-item {
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.product-item:hover {
    transform: translateY(-10px);
}

.product-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.product-item h3 {
    margin: 20px 0 10px;
    color: var(--primary-color);
}

.product-item p {
    padding: 0 20px 25px;
    color: var(--text-color);
}

.product-cta {
    margin-top: 20px;
}

/* Pain Points Section */
.pain-points {
    padding: 80px 0;
    background-color: var(--white);
}

.pain-points h2 {
    margin-bottom: 40px;
    text-align: center;
}

.challenges {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.challenge {
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: left;
}

.challenge:hover {
    transform: translateY(-5px);
}

.challenge h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #d9534f;
    margin-bottom: 15px;
}

.solution {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.solution h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* CTA Section */
.cta-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('images/cta-bg.jpg');
    background-size: cover;
    background-position: center;
    color: var(--white);
    padding: 80px 0;
    text-align: center;
}

.cta-content {
    max-width: 700px;
    margin: 0 auto;
}

.cta-content h2 {
    color: var(--white);
    margin-bottom: 1rem;
}

.cta-content p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Trust Indicators Section */
.trust-indicators {
    padding: 80px 0;
    text-align: center;
    background-color: var(--gray);
}

.certificates {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
    margin-top: 40px;
}

.certificate {
    text-align: center;
}

.certificate img {
    width: 100px;
    height: 100px;
    margin: 0 auto 15px;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 70px 0 20px;
    position: relative;
}

footer:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 50px;
    margin-bottom: 50px;
}

.footer-logo img {
    height: 80px;
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 5px;
}

.footer-links h3,
.footer-contact h3 {
    color: var(--white);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #ccc;
}

.footer-links ul li a:hover {
    color: var(--secondary-color);
}

.footer-contact p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-bottom {
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--secondary-color);
    color: var(--dark-color);
}

/* About Page Styles */
.about-story {
    padding: 80px 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.about-image img {
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.about-text h2 {
    text-align: left;
}

.about-text h2:after {
    left: 0;
    transform: none;
}

.mission-values {
    padding: 80px 0;
    background-color: var(--gray);
}

.mission {
    max-width: 800px;
    margin: 0 auto 60px;
    text-align: center;
}

.mission-statement {
    margin-top: 30px;
    padding: 25px;
    background-color: rgba(0, 86, 59, 0.05);
    border-left: 4px solid var(--primary-color);
    border-radius: 4px;
}

.highlighted-text {
    font-size: 1.15rem;
    font-style: italic;
    color: var(--dark-color);
    line-height: 1.8;
    font-weight: 500;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.value-item {
    background-color: var(--white);
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.value-item:hover {
    transform: translateY(-5px);
}

.value-item .icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.difference {
    padding: 80px 0;
}

.difference-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.difference-item {
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.difference-item h3 {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--secondary-color);
}

.difference-item:first-child h3 {
    color: #d9534f;
}

.difference-item:last-child h3 {
    color: var(--primary-color);
}

.difference-note {
    margin-top: 40px;
    padding: 25px;
    background-color: var(--light-color);
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--shadow);
}

.difference-note p {
    font-size: 1.2rem;
    margin-bottom: 0;
    color: var(--primary-color);
}

.difference-item ul li {
    margin-bottom: 10px;
    padding-left: 25px;
    position: relative;
}

.difference-item ul li:before {
    content: "\f00c";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.difference-item:first-child ul li:before {
    content: "\f00d";
    color: #d9534f;
}

.team {
    padding: 80px 0;
    background-color: var(--gray);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.team-member {
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    height: 250px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.team-member h3 {
    padding: 20px 20px 5px;
    margin-bottom: 0;
}

.team-member .position {
    color: var(--primary-color);
    font-weight: 600;
    padding: 0 20px;
    margin-bottom: 10px;
}

.team-member .bio {
    padding: 0 20px 20px;
}

/* Products Page Styles */
.product-intro {
    padding: 80px 0;
}

.intro-content {
    display: grid;
    grid-template-columns: 0.4fr 0.6fr;
    gap: 40px;
    align-items: center;
}

.intro-image img {
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.intro-text h2 {
    text-align: left;
}

.intro-text h2:after {
    left: 0;
    transform: none;
}

.rice-varieties {
    padding: 80px 0;
    background-color: var(--gray);
}

.varieties-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}

.variety-card {
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    display: grid;
    grid-template-columns: 1fr 2fr;
}

.variety-image {
    height: 100%;
}

.variety-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.variety-content {
    padding: 30px;
}

.variety-description {
    margin-bottom: 20px;
}

.variety-features {
    margin-bottom: 25px;
}

.variety-features li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.variety-features li i {
    color: var(--primary-color);
}

.packaging {
    padding: 80px 0;
}

.packaging-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.packaging-option {
    background-color: var(--white);
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.packaging-option:hover {
    transform: translateY(-5px);
}

.packaging-image {
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    overflow: hidden;
    border-radius: 50%;
}

.packaging-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.packaging-option ul {
    text-align: left;
    margin-top: 15px;
}

.packaging-option ul li {
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.packaging-option ul li:before {
    content: "\f054";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-size: 0.8rem;
}

.packaging-note {
    margin-top: 40px;
    padding: 20px;
    background-color: #fff3cd;
    border-left: 4px solid var(--secondary-color);
    border-radius: 4px;
}

.certifications {
    padding: 80px 0;
    background-color: var(--gray);
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.certification-item {
    background-color: var(--white);
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.certification-item:hover {
    transform: translateY(-5px);
}

.certification-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
}

.certification-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.compliance {
    padding: 80px 0;
}

.compliance-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.compliance-image img {
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.compliance-list {
    margin-top: 20px;
}

.compliance-list li {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.compliance-list li i {
    color: var(--primary-color);
    margin-top: 3px;
}

/* Contact Page Styles */
.contact-intro {
    padding: 60px 0;
    text-align: center;
}

.intro-content {
    max-width: 800px;
    margin: 0 auto;
}

.contact-main {
    padding: 0 0 80px;
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.contact-form-container {
    background-color: var(--white);
    padding: 40px;
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.contact-form-container h2 {
    text-align: left;
}

.contact-form-container h2:after {
    left: 0;
    transform: none;
}

.contact-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group.full-width {
    grid-column: span 2;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: 'Raleway', sans-serif;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input {
    width: auto;
}

.contact-info {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 40px;
    border-radius: 8px;
}

.contact-info h2 {
    color: var(--white);
    margin-bottom: 30px;
    text-align: left;
}

.contact-info h2:after {
    left: 0;
    transform: none;
}

.info-item {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.info-item .icon {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.info-item h3 {
    color: var(--white);
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.info-item p {
    margin-bottom: 5px;
}

.social-connect {
    margin-top: 30px;
}

.social-connect h3 {
    color: var(--white);
    margin-bottom: 15px;
}

.faq {
    padding: 80px 0;
    background-color: var(--gray);
}

.faq-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 900px;
    margin: 0 auto;
}

.faq-item {
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.faq-question {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.faq-question h3 {
    margin-bottom: 0;
    font-size: 1.2rem;
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 20px 20px;
    max-height: 500px;
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .about-content,
    .compliance-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .intro-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .variety-card {
        grid-template-columns: 1fr;
    }

    .variety-image {
        height: 300px;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .product-showcase {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2.2rem;
    }

    h2 {
        font-size: 1.9rem;
    }

    h2:after {
        width: 60px;
        height: 2px;
    }

    .hero {
        padding: 100px 0;
    }

    .hero h1 {
        font-size: 2.3rem;
    }

    .hero-cta {
        flex-direction: column;
        gap: 15px;
    }

    nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--white);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        padding: 20px 0;
        z-index: 999;
    }

    nav.active {
        display: block;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 10px 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .social-icons {
        justify-content: center;
        margin-top: 15px;
    }

    .product-item {
        max-width: 350px;
        margin: 0 auto;
    }
}

@media (max-width: 576px) {
    .container {
        width: 95%;
    }

    .hero {
        padding: 80px 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero .subtitle {
        font-size: 1.1rem;
    }

    .btn {
        padding: 12px 25px;
        font-size: 0.85rem;
    }

    .section-padding {
        padding: 60px 0;
    }

    .footer-logo img {
        height: 60px;
    }
}

@media (max-width: 576px) {
    .contact-form {
        grid-template-columns: 1fr;
    }

    .form-group.full-width {
        grid-column: span 1;
    }
}