<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Speed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #00563B;
        }
        .container {
            margin-top: 30px;
        }
        .result-container {
            margin-top: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .metric {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .metric-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .good {
            color: #4CAF50;
        }
        .average {
            color: #FF9800;
        }
        .poor {
            color: #F44336;
        }
        .progress-bar {
            height: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin-top: 5px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            border-radius: 5px;
        }
        .resources {
            margin-top: 30px;
        }
        .resource-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        button {
            background-color: #00563B;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #003c29;
        }
        input[type="url"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #00563B;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .recommendations {
            margin-top: 30px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }
        .recommendations h3 {
            margin-top: 0;
        }
        .recommendations ul {
            padding-left: 20px;
        }
        .recommendations li {
            margin-bottom: 10px;
        }
        .external-tools {
            margin-top: 30px;
        }
        .tool-link {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 15px;
            padding: 10px 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        .tool-link:hover {
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <h1>Rania Enterprises Website Speed Test</h1>
    <p>This tool helps you measure the performance of your website pages. Enter the URL of the page you want to test.</p>
    
    <div class="container">
        <input type="url" id="urlInput" placeholder="Enter URL (e.g., https://www.example.com)" value="http://localhost/index.html">
        <button id="testButton">Run Speed Test</button>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Testing page speed... This may take a few moments.</p>
        </div>
        
        <div class="result-container" id="resultContainer">
            <h2>Test Results</h2>
            
            <div class="metric">
                <div class="metric-name">Page Load Time</div>
                <div class="metric-value" id="loadTime">-</div>
                <div class="progress-bar">
                    <div class="progress" id="loadTimeProgress"></div>
                </div>
            </div>
            
            <div class="metric">
                <div class="metric-name">Time to First Byte (TTFB)</div>
                <div class="metric-value" id="ttfb">-</div>
                <div class="progress-bar">
                    <div class="progress" id="ttfbProgress"></div>
                </div>
            </div>
            
            <div class="metric">
                <div class="metric-name">DOM Content Loaded</div>
                <div class="metric-value" id="domLoaded">-</div>
                <div class="progress-bar">
                    <div class="progress" id="domLoadedProgress"></div>
                </div>
            </div>
            
            <div class="metric">
                <div class="metric-name">Total Page Size</div>
                <div class="metric-value" id="pageSize">-</div>
                <div class="progress-bar">
                    <div class="progress" id="pageSizeProgress"></div>
                </div>
            </div>
            
            <div class="metric">
                <div class="metric-name">Number of Requests</div>
                <div class="metric-value" id="requestCount">-</div>
                <div class="progress-bar">
                    <div class="progress" id="requestCountProgress"></div>
                </div>
            </div>
            
            <h3>Resource Breakdown</h3>
            <div id="resourceList"></div>
            
            <div class="recommendations" id="recommendations">
                <h3>Recommendations</h3>
                <ul id="recommendationsList"></ul>
            </div>
        </div>
        
        <div class="external-tools">
            <h2>Professional Testing Tools</h2>
            <p>For more comprehensive analysis, use these professional tools:</p>
            <a href="https://pagespeed.web.dev/" target="_blank" class="tool-link">Google PageSpeed Insights</a>
            <a href="https://www.webpagetest.org/" target="_blank" class="tool-link">WebPageTest</a>
            <a href="https://gtmetrix.com/" target="_blank" class="tool-link">GTmetrix</a>
            <a href="https://tools.pingdom.com/" target="_blank" class="tool-link">Pingdom Tools</a>
        </div>
    </div>
    
    <script>
        document.getElementById('testButton').addEventListener('click', function() {
            const url = document.getElementById('urlInput').value;
            if (!url) {
                alert('Please enter a valid URL');
                return;
            }
            
            // Show loading indicator
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';
            
            // In a real implementation, we would make an API call to a server
            // that would perform the actual speed test. For this demo, we'll
            // simulate the results after a delay.
            setTimeout(function() {
                simulateSpeedTest(url);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('resultContainer').style.display = 'block';
            }, 2000);
        });
        
        function simulateSpeedTest(url) {
            // Generate simulated results
            // In a real implementation, these would come from actual measurements
            const loadTime = Math.random() * 3 + 1; // 1-4 seconds
            const ttfb = Math.random() * 0.5 + 0.1; // 0.1-0.6 seconds
            const domLoaded = Math.random() * 1.5 + 0.5; // 0.5-2 seconds
            const pageSize = Math.random() * 3 + 0.5; // 0.5-3.5 MB
            const requestCount = Math.floor(Math.random() * 50) + 10; // 10-60 requests
            
            // Update the UI with the results
            document.getElementById('loadTime').textContent = loadTime.toFixed(2) + ' seconds';
            document.getElementById('loadTime').className = 'metric-value ' + getPerformanceClass(loadTime, 2, 3.5);
            document.getElementById('loadTimeProgress').style.width = Math.min(100, (loadTime / 5) * 100) + '%';
            document.getElementById('loadTimeProgress').style.backgroundColor = getProgressColor(loadTime, 2, 3.5);
            
            document.getElementById('ttfb').textContent = ttfb.toFixed(2) + ' seconds';
            document.getElementById('ttfb').className = 'metric-value ' + getPerformanceClass(ttfb, 0.3, 0.5);
            document.getElementById('ttfbProgress').style.width = Math.min(100, (ttfb / 1) * 100) + '%';
            document.getElementById('ttfbProgress').style.backgroundColor = getProgressColor(ttfb, 0.3, 0.5);
            
            document.getElementById('domLoaded').textContent = domLoaded.toFixed(2) + ' seconds';
            document.getElementById('domLoaded').className = 'metric-value ' + getPerformanceClass(domLoaded, 1, 2);
            document.getElementById('domLoadedProgress').style.width = Math.min(100, (domLoaded / 3) * 100) + '%';
            document.getElementById('domLoadedProgress').style.backgroundColor = getProgressColor(domLoaded, 1, 2);
            
            document.getElementById('pageSize').textContent = pageSize.toFixed(2) + ' MB';
            document.getElementById('pageSize').className = 'metric-value ' + getPerformanceClass(pageSize, 1.5, 2.5);
            document.getElementById('pageSizeProgress').style.width = Math.min(100, (pageSize / 5) * 100) + '%';
            document.getElementById('pageSizeProgress').style.backgroundColor = getProgressColor(pageSize, 1.5, 2.5);
            
            document.getElementById('requestCount').textContent = requestCount;
            document.getElementById('requestCount').className = 'metric-value ' + getPerformanceClass(requestCount, 30, 50);
            document.getElementById('requestCountProgress').style.width = Math.min(100, (requestCount / 100) * 100) + '%';
            document.getElementById('requestCountProgress').style.backgroundColor = getProgressColor(requestCount, 30, 50);
            
            // Generate resource breakdown
            generateResourceBreakdown(pageSize, requestCount);
            
            // Generate recommendations
            generateRecommendations(loadTime, ttfb, pageSize, requestCount);
        }
        
        function getPerformanceClass(value, goodThreshold, poorThreshold) {
            if (value <= goodThreshold) return 'good';
            if (value <= poorThreshold) return 'average';
            return 'poor';
        }
        
        function getProgressColor(value, goodThreshold, poorThreshold) {
            if (value <= goodThreshold) return '#4CAF50';
            if (value <= poorThreshold) return '#FF9800';
            return '#F44336';
        }
        
        function generateResourceBreakdown(totalSize, requestCount) {
            const resourceList = document.getElementById('resourceList');
            resourceList.innerHTML = '';
            
            // Calculate sizes for different resource types
            const imageSize = totalSize * (Math.random() * 0.3 + 0.4); // 40-70% of total
            const cssSize = totalSize * (Math.random() * 0.1 + 0.05); // 5-15% of total
            const jsSize = totalSize * (Math.random() * 0.2 + 0.1); // 10-30% of total
            const fontSize = totalSize * (Math.random() * 0.1 + 0.05); // 5-15% of total
            const otherSize = totalSize - imageSize - cssSize - jsSize - fontSize;
            
            // Calculate request counts
            const imageRequests = Math.floor(requestCount * (Math.random() * 0.3 + 0.3)); // 30-60% of requests
            const cssRequests = Math.floor(requestCount * (Math.random() * 0.1 + 0.05)); // 5-15% of requests
            const jsRequests = Math.floor(requestCount * (Math.random() * 0.2 + 0.1)); // 10-30% of requests
            const fontRequests = Math.floor(requestCount * (Math.random() * 0.1 + 0.05)); // 5-15% of requests
            const otherRequests = requestCount - imageRequests - cssRequests - jsRequests - fontRequests;
            
            // Create resource items
            createResourceItem(resourceList, 'Images', imageSize, imageRequests);
            createResourceItem(resourceList, 'CSS', cssSize, cssRequests);
            createResourceItem(resourceList, 'JavaScript', jsSize, jsRequests);
            createResourceItem(resourceList, 'Fonts', fontSize, fontRequests);
            createResourceItem(resourceList, 'Other', otherSize, otherRequests);
        }
        
        function createResourceItem(container, name, size, count) {
            const item = document.createElement('div');
            item.className = 'resource-item';
            item.innerHTML = `
                <strong>${name}:</strong> ${size.toFixed(2)} MB (${count} requests)
            `;
            container.appendChild(item);
        }
        
        function generateRecommendations(loadTime, ttfb, pageSize, requestCount) {
            const recommendationsList = document.getElementById('recommendationsList');
            recommendationsList.innerHTML = '';
            
            const recommendations = [];
            
            if (loadTime > 3) {
                recommendations.push('Optimize your page load time by reducing JavaScript and CSS files.');
            }
            
            if (ttfb > 0.4) {
                recommendations.push('Improve server response time (TTFB) by optimizing server configuration or using a CDN.');
            }
            
            if (pageSize > 2) {
                recommendations.push('Reduce total page size by compressing images and minifying CSS/JS files.');
            }
            
            if (requestCount > 40) {
                recommendations.push('Reduce the number of HTTP requests by combining files and using CSS sprites.');
            }
            
            // Add general recommendations
            recommendations.push('Use browser caching to improve load times for returning visitors.');
            recommendations.push('Implement lazy loading for images below the fold.');
            recommendations.push('Consider using a Content Delivery Network (CDN) for static assets.');
            
            // Add recommendations to the list
            recommendations.forEach(recommendation => {
                const li = document.createElement('li');
                li.textContent = recommendation;
                recommendationsList.appendChild(li);
            });
        }
    </script>
</body>
</html>
